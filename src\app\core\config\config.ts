import { Injectable, TransferState } from '@angular/core';
import { CONFIG_KEY } from './transfer-state-key';

@Injectable({ providedIn: 'root' })
export class Config {
  private _config: Record<string, string> | null = null;

  constructor(private transferState: TransferState) {}

  private get config(): Record<string, string> {
    if (this._config === null) {
      this._config = this.transferState.get(CONFIG_KEY, {
        BASE_API_URL: '',
        PRODUCTION: '',
        APTABASE_API_KEY: '',
        APTABASE_HOST: '',
        APTABASE_IS_DEBUG: '',
      });
    }
    return this._config;
  }

  get BASE_API_URL(): string {
    return this.config['BASE_API_URL'] || '';
  }

  get PRODUCTION(): string {
    return this.config['PRODUCTION'] || '';
  }

  get APTABASE_API_KEY(): string {
    return this.config['APTABASE_API_KEY'] || '';
  }

  get APTABASE_HOST(): string {
    return this.config['APTABASE_HOST'] || '';
  }

  get APTABASE_IS_DEBUG(): string {
    return this.config['APTABASE_IS_DEBUG'] || '';
  }
}
