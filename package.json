{"name": "website", "version": "0.0.0", "scripts": {"ng": "ng", "default-start:ssr": "npm run build && node dist/website/server/server.mjs", "start:ssr": "ng serve --port 4300", "build": "ng build --configuration prod", "watch": "ng build --watch --configuration dev", "test": "ng test"}, "prettier": {"overrides": [{"files": "*.html", "options": {"parser": "angular"}}]}, "private": true, "dependencies": {"@angular/common": "^20.1.0", "@angular/compiler": "^20.1.0", "@angular/core": "^20.1.0", "@angular/forms": "^20.1.0", "@angular/platform-browser": "^20.1.0", "@angular/platform-server": "^20.1.0", "@angular/router": "^20.1.0", "@angular/ssr": "^20.1.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@primeuix/themes": "^1.2.1", "dotenv": "^17.2.0", "express": "^5.1.0", "lucide-angular": "^0.525.0", "primeng": "^20.0.0-rc.3", "rxjs": "~7.8.0", "tailwindcss-animate": "^1.0.7", "tailwindcss-primeui": "^0.6.1", "tslib": "^2.3.0"}, "devDependencies": {"@angular/build": "^20.1.0", "@angular/cli": "^20.1.0", "@angular/compiler-cli": "^20.1.0", "@tailwindcss/postcss": "^4.1.11", "@types/express": "^5.0.1", "@types/jasmine": "~5.1.0", "@types/node": "^20.17.19", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint": "^9.31.0", "eslint-plugin-import": "^2.32.0", "jasmine-core": "~5.8.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "prettier": "^3.6.2", "tailwindcss": "^3.4.17", "typescript": "~5.8.2"}, "overrides": {"rollup": "npm:@rollup/wasm-node"}}