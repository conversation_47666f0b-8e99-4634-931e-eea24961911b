import { afterNextRender, Component, inject, signal } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { ThemeService } from './core/utils/theme';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet],
  templateUrl: './app.html',
  styleUrl: './app.scss',
})
export class App {
  protected readonly title = signal('website');
  private themeService = inject(ThemeService);

  constructor() {
    // Initialize theme after the component is rendered client side
    afterNextRender(() => {
      this.themeService.initTheme();
    });
  }
}
