# Stage 1: Build the Angular app
FROM node:20-alpine AS builder

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm ci

# Copy the rest of the source code
COPY . .

# Build the Angular SSR app (adjust the build commands if needed)
RUN npm run build

# Stage 2: Run the app with a minimal image
FROM node:20-alpine

WORKDIR /app

# Copy only the built files and necessary files from the builder stage
COPY --from=builder /app/dist /app/dist
COPY --from=builder /app/package*.json ./

# Install only production dependencies
RUN npm ci --omit=dev

ARG PORT=4300
ENV PORT=$PORT

# Expose the port your SSR server listens on (default is 4300)
EXPOSE $PORT

# Start the SSR server
CMD ["node", "dist/website/server/server.mjs"]
