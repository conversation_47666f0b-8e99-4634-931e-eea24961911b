@use "./tailwind.css";
@use "./styles/theme-overrides.scss";
@use "./styles/variables.scss";
@import url("https://fonts.googleapis.com/css2?family=EB+Garamond:ital,wght@0,400..800;1,400..800&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Bonheur+Royale&family=EB+Garamond:ital,wght@0,400..800;1,400..800&display=swap");

* {
  font-family: "EB Garamond", serif !important;
  font-optical-sizing: auto;
  box-sizing: border-box;
  color: #2b2b29;
  margin: 0;
  padding: 0;
  font-size: 16px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Bonheur Royale", cursive !important;
  font-optical-sizing: auto !important;
  padding: 0;
  margin: 0 !important;
  margin-bottom: 0 !important;
}

// Variables
$font-weight-regular: 400; // Equivalente a 'normal'
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700; // Equivalente a 'bold'
$font-weight-extrabold: 800;
$font-weight-black: 900;

h1 {
  font-size: 2rem; /* ~32 px */
  font-weight: $font-weight-extrabold;
}

h2 {
  font-size: 1.74rem; /* ~28 px */
  font-weight: $font-weight-bold;
}

h3 {
  font-size: 1.52rem; /* ~24 px */
  font-weight: $font-weight-semibold;
}

h4 {
  font-size: 1.32rem; /* ~21 px */
  font-weight: $font-weight-medium;
}

h5 {
  font-size: 1.15rem; /* ~18 px */
  font-weight: $font-weight-medium;
}

h6 {
  font-size: 1rem; /* ~16 px */
  font-weight: $font-weight-regular;
}
