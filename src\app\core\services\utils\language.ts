import { Injectable } from '@angular/core';
import { CheckUtils } from '@core/utils/check';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs';

const _LANGUAGE: string = '_language';

@Injectable({
  providedIn: 'root',
})
export class LanguageService {
  language$: Subject<languageCodeType> = new Subject();
  languages: ILanguage[] = [];
  currentLanguage?: ILanguage;

  constructor(private translateService: TranslateService) {}

  configureLang(languages: ILanguage[]) {
    this.languages = languages;
  }

  instantTranslate(key: string): string {
    return this.translateService.instant(key);
  }

  loadLanguage(): Promise<Event> {
    return new Promise<Event>((resolve, _reject) => {
      let storageLanguage: string = localStorage.getItem(_LANGUAGE) || '';
      if (!CheckUtils.isNullUndefinedOrEmpty(storageLanguage)) {
        this.currentLanguage = this.#getLanguageByCode(storageLanguage);
        this.setLanguage(this.currentLanguage);
      } else {
        this.#setDefaultLanguage();
      }
      resolve(<any>true);
    });
  }

  #setDefaultLanguage() {
    let l = this.languages?.find((l) => l.default);
    this.setLanguage(l!);
  }

  /**Get Language Code from a string formatted as 'code-countrycode'. Example 'it-IT' returns 'it' */
  #getLanguageByCode(code: string): ILanguage {
    return this.languages?.find((l) => l.code == code)!;
  }

  setLanguage(language: ILanguage): void {
    this.languages.forEach((l) => (l.active = false));
    language.active = true;
    this.currentLanguage = language;
    this.#updateTranslateService(language);
    this.#setCookieLanguage(language.code);
    this.language$.next(language.code);
  }

  #updateTranslateService(language: ILanguage) {
    this.translateService.setDefaultLang(language.code);
    this.translateService.use(language.code);
  }

  #setCookieLanguage(value: string): void {
    localStorage.setItem(_LANGUAGE, value);
  }
}

/** Define the language object */
export type ILanguage = {
  name?: string;
  code: languageCodeType;
  enabled?: boolean;
  default?: boolean;
  countryCode?: languageCountryCodeType;
  active?: boolean;
};

/** Define the LanguageCode Type */
export enum languageCodeType {
  it = 'it',
  en = 'en',
}

/** Define thee LanguageCountryCode Type */
export enum languageCountryCodeType {
  IT = 'IT',
  GB = 'GB',
}

/** Define thee LanguageName Type */
export enum languageNameType {
  it = 'Italiano',
  en = 'English',
}
