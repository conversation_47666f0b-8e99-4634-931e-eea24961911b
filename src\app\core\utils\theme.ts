import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ThemeService {
  public initTheme() {
    const THEME_STATE_KEY = 'theme_state';
    const storedConfig = localStorage.getItem(THEME_STATE_KEY);

    if (storedConfig) {
      const parsedConfig = JSON.parse(storedConfig);
      if (parsedConfig.darkTheme) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }
  }
}
