import {
  ApplicationConfig,
  inject,
  mergeApplicationConfig,
  provideAppInitializer,
  TransferState,
} from '@angular/core';
import { provideServerRendering, withRoutes } from '@angular/ssr';
import { appConfig } from './app.config';
import { serverRoutes } from './app.routes.server';
import { CONFIG_KEY } from './core/config/transfer-state-key';

export const transferConfigFactory = (transferState: TransferState) => {
  return () => {
    transferState.set(CONFIG_KEY, {
      BASE_API_URL: process.env.BASE_API_URL || '',
      PRODUCTION: process.env.PRODUCTION || '',
      APTABASE_API_KEY: process.env.APTABASE_API_KEY || '',
      APTABASE_HOST: process.env.APTABASE_HOST || '',
      APTABASE_IS_DEBUG: process.env.APTABASE_IS_DEBUG || '',
    });
  };
};

const serverConfig: ApplicationConfig = {
  providers: [
    provideServerRendering(withRoutes(serverRoutes)),
    provideAppInitializer(() => {
      const transferState = inject(TransferState);
      return transferConfigFactory(transferState)();
    }),
  ],
};

export const config = mergeApplicationConfig(appConfig, serverConfig);
