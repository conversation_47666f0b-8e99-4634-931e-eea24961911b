{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "newProjectRoot": "projects", "projects": {"website": {"architect": {"build": {"builder": "@angular/build:application", "configurations": {"dev": {"extractLicenses": false, "optimization": false, "sourceMap": true}, "prod": {"budgets": [{"maximumError": "1MB", "maximumWarning": "500kB", "type": "initial"}, {"maximumError": "8kB", "maximumWarning": "4kB", "type": "anyComponentStyle"}], "outputHashing": "all"}}, "defaultConfiguration": "prod", "options": {"assets": [{"glob": "**/*", "input": "src/assets", "output": "assets"}, {"glob": "**/*", "input": "public"}], "browser": "src/main.ts", "inlineStyleLanguage": "scss", "outputMode": "server", "server": "src/main.server.ts", "ssr": {"entry": "src/server.ts"}, "styles": ["src/styles.scss"], "tsConfig": "tsconfig.app.json"}}, "extract-i18n": {"builder": "@angular/build:extract-i18n"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"dev": {"buildTarget": "website:build:dev"}, "prod": {"buildTarget": "website:build:prod"}}, "defaultConfiguration": "dev"}, "test": {"builder": "@angular/build:karma", "options": {"assets": [{"glob": "**/*", "input": "public"}], "inlineStyleLanguage": "scss", "styles": ["src/styles.scss"], "tsConfig": "tsconfig.spec.json"}}}, "prefix": "app", "projectType": "application", "root": "", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "sourceRoot": "src"}}, "version": 1}