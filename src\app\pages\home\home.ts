import { Component } from '@angular/core';
import { Config } from '@core/config/config';

@Component({
  selector: 'app-home',
  imports: [],
  templateUrl: './home.html',
  styleUrl: './home.scss',
})
export class Home {
  constructor(public config: Config) {
    console.log('base api url', this.config.BASE_API_URL);
    console.log('production', this.config.PRODUCTION);
    console.log('aptabase key', this.config.APTABASE_API_KEY);
    console.log('aptabase host', this.config.APTABASE_HOST);
    console.log('aptabase debug', this.config.APTABASE_IS_DEBUG);
  }
}
